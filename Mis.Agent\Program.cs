using Mis.Agent.Services;
using Microsoft.AspNetCore;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Mis.Agent.ApplicationsContext;
using Serilog;
using Serilog.Events;
using System;
using System.Threading.Tasks;
using Volo.Abp;

namespace Mis.Agent
{

    public static class Program
    {
        static IConfiguration configuration;
        static IHost _webHost;

        [STAThread]
        static void Main()
        {

            Log.Logger = new LoggerConfiguration()
#if DEBUG
                .MinimumLevel.Debug()
#else
                .MinimumLevel.Information()
#endif
                .MinimumLevel.Override("Microsoft", LogEventLevel.Information)
                .MinimumLevel.Override("Microsoft.EntityFrameworkCore", LogEventLevel.Warning)
                .Enrich.FromLogContext()
                .WriteTo.Async(c => c.File("Logs/logs.txt"))
                .WriteTo.Async(c => c.Console())
                .CreateLogger();
            // Build the configuration


            //var directoryPath = Directory.GetCurrentDirectory();
            //var correctedPath = Path.Combine(Path.GetFullPath(Path.Combine(directoryPath, @"..\..\..\..\Mis.Agent\")), "appsettings.json");




            //configuration = new ConfigurationBuilder()
            //.SetBasePath(Path.GetDirectoryName(correctedPath)) // Ensure the base path is set
            //    .AddJsonFile(Path.GetFileName(correctedPath), optional: false, reloadOnChange: true) // Configuration file
            //.Build();
            var exePath = AppContext.BaseDirectory;
            var configPath = Path.Combine(exePath, "appsettings.json");

            if (!File.Exists(configPath))
            {
                MessageBox.Show($"Configuration file not found: {configPath}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                Environment.Exit(1); // Or handle it gracefully
            }

            configuration = new ConfigurationBuilder()
                .SetBasePath(exePath)
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .Build();

            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            var baseUrl = configuration.GetSection("Server").GetValue<string>("BaseUrl"); // Example method to get base URL from configuration
            if (baseUrl == null) { throw new UserFriendlyException("Base Url Is Empty"); }
            var host = CreateHostBuilder(baseUrl).Build();
            Task.Run(() => StartHostAsync(host));
            using var serviceScope = host.Services.CreateScope();
            var serviceProvider = serviceScope.ServiceProvider;

            try
            {
                // Resolve and run the main form
                var agentForm = new AgentForm(serviceProvider);
                Application.Run(agentForm);
            }
            catch (Exception ex)
            {
                // Log or display the exception
                Console.WriteLine($"An error occurred: {ex.Message}");
                MessageBox.Show($"An error occurred: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private static async Task StartHostAsync(IHost host)
        {
            try
            {
                await host.StartAsync();
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "Host failed to start");
                throw;
            }
        }

        public static IHostBuilder CreateHostBuilder(string baseUrl) =>
       Host.CreateDefaultBuilder()
           .UseSerilog((context, config) =>
               config.ReadFrom.Configuration(context.Configuration)
                     .WriteTo.Console()
                     .WriteTo.File("logs/log.txt", rollingInterval: RollingInterval.Day))
           .ConfigureWebHostDefaults(webBuilder =>
           {
               webBuilder.UseStartup<Startup>()
                         .UseUrls(baseUrl); // Keep your dynamic base URL here
           });

    }
}
