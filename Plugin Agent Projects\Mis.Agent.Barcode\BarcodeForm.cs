
using System.Diagnostics;

namespace Mis.Agent.Barcode
{
    public partial class BarcodeForm : Form
    {
        private readonly IBarcodeAppService _barcodeAppService;

        public BarcodeForm(IBarcodeAppService barcodeAppService)
        {
            _barcodeAppService = barcodeAppService;
            InitializeComponent();
            InitializeLocalization();
        }

        private void InitializeLocalization()
        {
            // Subscribe to culture changes
            SimpleLocalization.CultureChanged += OnCultureChanged;

            // Apply initial localization
            ApplyLocalization();
        }

        private void OnCultureChanged(object? sender, CultureChangedEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnCultureChanged(sender, e)));
                return;
            }

            ApplyLocalization();
        }

        private void ApplyLocalization()
        {
            // Update form title
            this.Text = SimpleLocalization.GetString("BarcodeFormTitle", "Barcode Configuration");

            // Update tab text - ensure it's never null
            var tabText = SimpleLocalization.GetString("BarcodeTabText", "Barcode Settings");
            BarcodeTab.Text = string.IsNullOrEmpty(tabText) ? "Barcode Settings" : tabText;

            // Update labels
            label4.Text = SimpleLocalization.GetString("BarcodeUrlLabel", "Barcode URL:");
            label5.Text = SimpleLocalization.GetString("ComPortLabel", "COM Port:");
            label6.Text = SimpleLocalization.GetString("AvailablePortsLabel", "Available Ports:");

            // Update buttons
            SaveBarcodeConfiguration.Text = SimpleLocalization.GetString("SaveBarcodeConfigButton", "Save Barcode Configuration");
            button3.Text = SimpleLocalization.GetString("TestConnectionButton", "Test Connection");
        }
        private async void SaveBarcodeConfiguration_Click(object sender, EventArgs e)
        {
            try
            {
                // Validate and update the URL
                string newBaseUrl = barcodeUrlTextBox.Text;
                bool result = _barcodeAppService.IsValidBarcodeUrl(newBaseUrl);

                if (string.IsNullOrEmpty(newBaseUrl) /*|| !result*/)
                {
                    string message = SimpleLocalization.GetString("InvalidUrlMessage", "Please enter a valid URL.");
                    string title = SimpleLocalization.GetString("Error", "Error");
                    MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Update the BaseUrl in appsettings.json if it's different
                var currentUrl = _barcodeAppService.GetBarcodeBaseUrl();
                if (currentUrl != newBaseUrl)
                {
                    _barcodeAppService.UpdateBarcodeBaseUrl(newBaseUrl);
                    string message = SimpleLocalization.GetString("ConfigurationSavedMessage", "Configuration saved successfully!");
                    string title = SimpleLocalization.GetString("Success", "Success");
                    MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                // Get the selected COM port from the ComboBox
                string selectedComPort = comboBoxCOMPorts.SelectedItem as string;
                if (string.IsNullOrEmpty(selectedComPort))
                {
                    string message = SimpleLocalization.GetString("NoPortSelectedMessage", "Please select a COM port.");
                    string title = SimpleLocalization.GetString("Warning", "Warning");
                    MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                _barcodeAppService.UpdateCOMPort(selectedComPort);


                // Display the selected COM port in textBox5
                comPortTextBox.Text = selectedComPort;
                //// Start listening for data on the COM port
                //_barcodeAppService.PublicInitialize(newBaseUrl, selectedComPort,false);
                //await connection.InvokeAsync("SendMessage", message);
                RestartApplication();

            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to update configuration and send message: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        private void RestartApplication()
        {
            try
            {

                // Close the current application
                Application.Exit();

                // Start a new instance of the application
                Process.Start(Application.ExecutablePath);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to restart the application: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                // Optionally log the exception
            }
        }

    }
}
