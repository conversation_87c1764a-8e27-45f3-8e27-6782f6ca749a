using Mis.Agent.PluginSystem;
using Mis.Agent.Localization;
using Mis.Agent.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using System;
using System.Reflection;
using System.Windows.Forms;
using System.Xml.Linq;

namespace Mis.Agent
{
    public partial class AgentForm : Form
    {
        private IServiceProvider _serviceProvider;
        private IHost _webHost;
        private bool _isArabic = false;

        public AgentForm()
        {
            InitializeComponent();
            InitializeLocalization();
            tabControl1.SelectedIndexChanged += TabControl1_SelectedIndexChanged; // Attach the event handler
        }

        public AgentForm(IServiceProvider serviceProvider) : this()
        {
            _serviceProvider = serviceProvider;
        }

        private void InitializeLocalization()
        {
            // Subscribe to culture changes
            SimpleLocalization.CultureChanged += OnCultureChanged;

            // Apply initial localization
            ApplyLocalization();
        }

        private void OnCultureChanged(object sender, CultureChangedEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnCultureChanged(sender, e)));
                return;
            }

            ApplyLocalization();
            ApplyRtlLayout(e.IsRightToLeft);
        }

        private void ApplyRtlLayout(bool isRtl)
        {
            if (isRtl)
            {
                RightToLeft = RightToLeft.Yes;
                RightToLeftLayout = true;
            }
            else
            {
                RightToLeft = RightToLeft.No;
                RightToLeftLayout = false;
            }

            // Apply RTL to all child controls
            ApplyRtlToControls(this.Controls, isRtl);
        }

        private void ApplyRtlToControls(Control.ControlCollection controls, bool isRtl)
        {
            foreach (Control control in controls)
            {
                if (control is TabControl tabControl)
                {
                    tabControl.RightToLeft = isRtl ? RightToLeft.Yes : RightToLeft.No;
                    tabControl.RightToLeftLayout = isRtl;

                    foreach (TabPage tabPage in tabControl.TabPages)
                    {
                        tabPage.RightToLeft = isRtl ? RightToLeft.Yes : RightToLeft.No;
                        ApplyRtlToControls(tabPage.Controls, isRtl);
                    }
                }
                else
                {
                    control.RightToLeft = isRtl ? RightToLeft.Yes : RightToLeft.No;
                }

                if (control.HasChildren)
                {
                    ApplyRtlToControls(control.Controls, isRtl);
                }
            }
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            if (_webHost != null)
            {
                _webHost.StopAsync();
                _webHost.Dispose();
            }
            base.OnFormClosing(e);
        }
        private async void TabControl1_SelectedIndexChanged(object? sender, EventArgs e)
        {
            try
            {
                RefreshDataGridView();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"An error occurred while refreshing data: {ex.Message}");
            }
        }


        private async void RefreshDataGridView()
        {
            try
            {
                // Fetch updated data and bind it to the DataGridView
                //var notifications = _publicNotificationAppService.GetAllNotificationsAsync().Result; // Implement this method
                var notifications = await SerialPortManager.Instance.GetAllNotificationsAsync();

                var notificationsTab = tabControl1.TabPages["NotificationsTab"] as TabPage;
                if (notificationsTab != null)
                {
                    var dataGridView = notificationsTab.Controls["dataGridView1"] as DataGridView;
                    if (dataGridView != null)
                    {
                        if (dataGridView.InvokeRequired)
                        {
                            dataGridView.Invoke(new Action(() =>
                            {
                                dataGridView.DataSource = notifications;
                                dataGridView.Refresh();
                            }));
                        }
                        else
                        {
                            dataGridView.DataSource = notifications;
                            dataGridView.Refresh();
                        }
                    }
                    else
                    {
                        Console.WriteLine("PictureBox not found.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                else
                {
                    Console.WriteLine("ScannerTab not found.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error refreshing data: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }



        private async void AgentForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Initialize the plugin system
                await InitializePluginSystem();

                // Apply initial localization
                ApplyLocalization();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error initializing application: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task InitializePluginSystem()
        {
            string pluginPath = Path.Combine(Application.StartupPath, "Plugins");

            if (!Directory.Exists(pluginPath))
            {
                MessageBox.Show("Plugins folder not found.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            // Set up the plugin manager with service provider
            var pluginManager = PluginManager.Instance;
            pluginManager.SetServiceProvider(_serviceProvider);

            // Subscribe to plugin events
            pluginManager.PluginLoaded += OnPluginLoaded;
            pluginManager.PluginLoadFailed += OnPluginLoadFailed;

            // Discover and load plugins
            pluginManager.DiscoverPlugins(pluginPath);
            pluginManager.LoadAutoLoadPlugins();

            // Add plugin tab pages to the form
            var tabPages = pluginManager.GetTabPages();
            Console.WriteLine($"Found {tabPages.Count} tab pages from plugins");

            foreach (var tabPage in tabPages)
            {
                Console.WriteLine($"Adding tab page: {tabPage.Text}");
                tabControl1.TabPages.Add(tabPage);
            }

            Console.WriteLine($"Total tabs in control: {tabControl1.TabPages.Count}");

            // Debug tab information
            for (int i = 0; i < tabControl1.TabPages.Count; i++)
            {
                var tab = tabControl1.TabPages[i];
                Console.WriteLine($"Tab {i}: Text='{tab.Text}', Name='{tab.Name}', Visible={tab.Visible}");
            }

            // Force refresh the tab control
            tabControl1.Refresh();

            await Task.CompletedTask;
        }

        private void OnPluginLoaded(object? sender, PluginLoadedEventArgs e)
        {
            Console.WriteLine($"Plugin loaded successfully: {e.Plugin.Name} v{e.Plugin.Version}");
        }

        private void OnPluginLoadFailed(object? sender, PluginLoadFailedEventArgs e)
        {
            Console.WriteLine($"Failed to load plugin {e.Plugin.Name}: {e.Exception.Message}");
            // Optionally show user-friendly notification instead of console
            ShowNotification("Plugin Error", $"Failed to load plugin: {e.Plugin.Name}");
        }

        /// <summary>
        /// Shows a notification using the plugin system or fallback to message box
        /// </summary>
        private void ShowNotification(string title, string message)
        {
            try
            {
                PluginManager.Instance.ShowNotification(title, message);
            }
            catch
            {
                // Fallback to message box if plugin notifications fail
                MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }


        private void ApplyLocalization()
        {
            // Update form title
            this.Text = SimpleLocalization.GetString("FormTitle", "MIS Agent");

            // Update buttons
            btnSaveAllSettings.Text = SimpleLocalization.GetString("BtnSaveAllSettings.Text", "Save All Settings");

            // Update language switch button text based on current language
            if (SimpleLocalization.CurrentCulture.Name == "ar-SA")
            {
                btnSwitchLanguage.Text = SimpleLocalization.GetString("SwitchToEnglish", "Switch to English");
            }
            else
            {
                btnSwitchLanguage.Text = SimpleLocalization.GetString("SwitchToArabic", "Switch to Arabic");
            }

            // Update tab pages
            LocalizeTabPages();
        }

        private void LocalizeTabPages()
        {
            foreach (TabPage tab in tabControl1.TabPages)
            {
                // Try different resource key patterns for tabs
                string[] possibleKeys = {
                    tab.Name,
                    tab.Name + "Tab",
                    "Tab" + tab.Name,
                    tab.Name.Replace("Tab", "")
                };

                foreach (string key in possibleKeys)
                {
                    string localizedText = SimpleLocalization.GetString(key);
                    if (localizedText != key)
                    {
                        tab.Text = localizedText;
                        break;
                    }
                }
            }
        }

        private void BtnSwitchLanguage_Click(object sender, EventArgs e)
        {
            try
            {
                // Toggle language
                string newCulture = SimpleLocalization.CurrentCulture.Name == "ar-SA" ? "en-US" : "ar-SA";
                SimpleLocalization.SetCulture(newCulture);

                // Update the flag
                _isArabic = newCulture == "ar-SA";

                // Show notification
                string title = SimpleLocalization.GetString("LanguageSwitched", "Language Switched");
                string message = SimpleLocalization.GetString("LanguageSwitchedMessage", "Language has been switched successfully.");
                MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                string errorTitle = SimpleLocalization.GetString("Error", "Error");
                MessageBox.Show($"Error switching language: {ex.Message}", errorTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }


    }
}
