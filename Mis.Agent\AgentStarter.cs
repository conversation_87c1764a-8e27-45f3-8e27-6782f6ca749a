using System;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using System.Threading;
using System.Windows.Forms;
using Mis.Agent;
using Microsoft.Extensions.Hosting;
using Microsoft.AspNetCore.Hosting;
using static System.Windows.Forms.VisualStyles.VisualStyleElement;
using Mis.Agent.PluginSystem;
using Microsoft.Extensions.Configuration;
using System.Configuration;
using Mis.Agent.Models;
using Mis.Agent.Services;
using System.IO.Ports;
using Microsoft.Extensions.DependencyInjection;
using System.Reflection;

namespace Mis.Agent.ApplicationsContext
{
    public class NotificationEventArgs : EventArgs
    {
        public bool IsEnabled { get; set; }
    }

    public static class NotificationManager
    {
        public static event EventHandler<NotificationEventArgs>? NotificationEvent;

        public static void Notify(NotificationEventArgs e)
        {
            NotificationEvent?.Invoke(null, e);
        }
    }

    public class AgentStarter : ApplicationContext
    {
        private IHost? _webHost;
        private NotifyIcon? trayIcon;
        private TransactionDto? _transactionDto;
        private AgentForm? _agentForm;
        IServiceProvider _serviceProvider;
        bool _notificationsEnabled = true;
        public AgentStarter()
        {
            SerialPortManager.Instance.BarcodeImageCaptured += OnImageCaptured;
            SerialPortManager.Instance.scannerImageCaptured += OnImageCaptured;
            SerialPortManager.Instance.NotificationUpdated += OnNotificationUpdated;
            NotificationManager.NotificationEvent += OnNotificationReceived;
            StartSignalRServer();
            InitializeTrayIcon();
        }
        private async void StartSignalRServer()
        {
            try
            {
                // Start the SignalR server
                _webHost = Host.CreateDefaultBuilder()
                    .ConfigureWebHostDefaults(webBuilder =>
                    {
                        webBuilder.UseStartup<Startup>();
                    })
                    .Build();

                _webHost.Start();
                await ExecutePlugin();
            }
            catch (HttpRequestException ex)
            {
                // Handle exceptions related to HTTP requests
                MessageBox.Show($"Failed to connect to SignalR server: {ex.Message}", "Connection Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            catch (Exception ex)
            {
                // Handle any other exceptions
                MessageBox.Show($"An error occurred: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        private async Task ExecutePlugin()
        {
            string pluginPath = Path.Combine(Application.StartupPath, "Plugins");
            var services = new ServiceCollection();
            if (!Directory.Exists(pluginPath))
            {
                Directory.CreateDirectory(pluginPath);
                Console.WriteLine("Plugins folder not found. I will Created For You ", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            // Initialize the plugin system
            var pluginManager = PluginManager.Instance;
            pluginManager.SetServiceProvider(services.BuildServiceProvider());

            // Discover and load plugins
            pluginManager.DiscoverPlugins(pluginPath);
            pluginManager.LoadAutoLoadPlugins();

            Console.WriteLine($"Loaded {pluginManager.LoadedPlugins.Count} plugins successfully.");
        }

        private void OnNotificationReceived(object? sender, NotificationEventArgs e)
        {
            _notificationsEnabled = e.IsEnabled; // Additional logic based on notification state
        }
        public void ShowNotification(string title, string text)
        {
            if (_notificationsEnabled)
            {
                using (var notifyIcon = new NotifyIcon
                {
                    Icon = SystemIcons.Information,
                    Visible = true,
                    BalloonTipTitle = title,
                    BalloonTipText = text
                })
                {
                    notifyIcon.ShowBalloonTip(1000);
                    Task.Delay(1000).ContinueWith(t => notifyIcon.Dispose());
                }
            }
        }
        private void InitializeTrayIcon()
        {
            var contextMenu = new ContextMenuStrip();
            contextMenu.Items.AddRange(new ToolStripItem[] {
            new ToolStripMenuItem("Show Tabs Form", null, ShowTabsForm),
            new ToolStripMenuItem("Exit", null, Exit)
        });

            trayIcon = new NotifyIcon
            {
                Icon = Icon.ExtractAssociatedIcon(Application.ExecutablePath),
                ContextMenuStrip = contextMenu,
                Visible = true,
                Text = "Agent Application"
            };

            trayIcon.MouseClick += TrayIcon_MouseClick;
        }



        private async void TrayIcon_MouseClick(object? sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                await ShowOrRefreshTabsFormAsync();
            }
        }

        private async void ShowTabsForm(object? sender, EventArgs e)
        {
            try
            {
                bool result = await ShowOrRefreshTabsFormAsync();
                MessageBox.Show(result ? "TabsForm is already opened." : "TabsForm operation failed.");
            }
            catch (Exception ex)
            {
                MessageBox.Show($" An error occurred in ShowTabsForm: {ex.Message}");
            }
        }

        public async Task<bool> ShowOrRefreshTabsFormAsync()
        {
            try
            {
                if (_agentForm == null || _agentForm.IsDisposed)
                {
                    return await RunTabsFormAsync(_transactionDto);
                }
                else
                {
                    if (_agentForm.InvokeRequired)
                    {
                        _agentForm.Invoke(new Action(() =>
                        {
                            _agentForm.BringToFront();
                            _agentForm.Refresh(); // Implement Refresh logic in the form if needed
                        }));
                    }
                    else
                    {
                        _agentForm.BringToFront();
                        _agentForm.Refresh(); // Implement Refresh logic in the form if needed
                    }
                    return true;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($" An error occurred in ShowOrRefreshTabsFormAsync: {ex.Message}");

                return true;
            }

        }

        private async Task<bool> RunTabsFormAsync(TransactionDto? transactionDto)
        {
            try
            {
                var taskCompletionSource = new TaskCompletionSource<bool>();

                var transactionFormThread = new Thread(() =>
                {
                    try
                    {
                        Application.EnableVisualStyles();
                        Application.SetCompatibleTextRenderingDefault(false);
                        _agentForm = new AgentForm()
                        {
                            StartPosition = FormStartPosition.CenterScreen
                        };
                        Application.Run(_agentForm);
                        taskCompletionSource.SetResult(true);
                    }
                    catch (Exception ex)
                    {
                        taskCompletionSource.SetException(ex);
                    }
                });

                transactionFormThread.SetApartmentState(ApartmentState.STA);
                transactionFormThread.Start();

                return await taskCompletionSource.Task;
            }
            catch (Exception ex)
            {
                MessageBox.Show($" An error occurred in RunTabsFormAsync: {ex.Message}");
                return false;
            }

        }


        private void Exit(object? sender, EventArgs e)
        {
            try
            {
                trayIcon.Visible = false;
                Application.Exit();
                Environment.Exit(0);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception in Exit method: {ex.Message}");
            }
        }
        private void OnImageCaptured(Image capturedImage)
        {
            try
            {
                // Access the ScannerTab by its name
                var scannerTab = _agentForm?.tabControl1.TabPages["ScannerTab"] as TabPage;
                if (scannerTab != null)
                {
                    var pictureBox = scannerTab.Controls["pictureScanned"] as PictureBox;
                    if (pictureBox != null)
                    {
                        pictureBox.Image = capturedImage;
                    }
                    else
                    {
                        MessageBox.Show("PictureBox not found.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                else
                {
                    Console.WriteLine("ScannerTab not found.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error occurred: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        private void OnNotificationUpdated()
        {
            RefreshDataGridView();
        }
        private async void RefreshDataGridView()
        {
            try
            {
                // Fetch updated data and bind it to the DataGridView
                //var notifications = _publicNotificationAppService.GetAllNotificationsAsync().Result; // Implement this method
                var notifications = await SerialPortManager.Instance.GetAllNotificationsAsync();

                var notificationsTab = _agentForm?.tabControl1.TabPages["NotificationsTab"] as TabPage;
                if (notificationsTab != null)
                {
                    var dataGridView = notificationsTab.Controls["dataGridView1"] as DataGridView;
                    if (dataGridView != null)
                    {
                        if (dataGridView.InvokeRequired)
                        {
                            dataGridView.Invoke(new Action(() =>
                            {
                                dataGridView.DataSource = notifications;
                                dataGridView.Refresh();
                            }));
                        }
                        else
                        {
                            dataGridView.DataSource = notifications;
                            dataGridView.Refresh();
                        }
                    }
                    else
                    {
                        Console.WriteLine("PictureBox not found.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                else
                {
                    Console.WriteLine("ScannerTab not found.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error refreshing data: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

    }
}