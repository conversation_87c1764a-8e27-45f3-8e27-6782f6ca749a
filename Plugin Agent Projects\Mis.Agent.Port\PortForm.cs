
using System.Diagnostics;
using System.Reflection;
using System.Windows.Forms;
using System.Xml.Linq;

namespace Mis.Agent.Port
{
    public partial class PortForm : Form
    {
        private IPortAppService _portAppService;
        public PortForm(IPortAppService portAppService)
        {
            _portAppService = portAppService;
            InitializeComponent();
            InitializeLocalization();
            textBox3.Text = _portAppService.GetBaseUrl();
        }

        private void InitializeLocalization()
        {
            // Subscribe to culture changes
            SimpleLocalization.CultureChanged += OnCultureChanged;

            // Apply initial localization
            ApplyLocalization();
        }

        private void OnCultureChanged(object? sender, CultureChangedEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnCultureChanged(sender, e)));
                return;
            }

            ApplyLocalization();
        }

        private void ApplyLocalization()
        {
            // Update form title
            this.Text = SimpleLocalization.GetString("PortFormTitle", "Port Configuration");

            // Update tab text
            PortTap.Text = SimpleLocalization.GetString("PortTabText", "Port Settings");

            // Update labels
            label3.Text = SimpleLocalization.GetString("BaseUrlLabel", "Base URL:");

            // Update buttons
            savePortConfiguration.Text = SimpleLocalization.GetString("SavePortConfigButton", "Save Port Configuration");
        }

        private void savePortConfiguration_Click(object sender, EventArgs e)
        {
            string newUrl = textBox3.Text;

            if (!string.IsNullOrWhiteSpace(newUrl))
            {
                try
                {
                    var currentUrl = _portAppService.GetBaseUrl();

                    if (currentUrl != newUrl)
                    {
                        // Update the BaseUrl in appsettings.json
                        _portAppService.UpdateBaseUrl(newUrl);

                        // Confirm the update
                        _portAppService.ShowNotification("Settings Saved", "URL updated successfully. Restarting the application...");
                        // Restart the application
                        RestartApplication();
                    }
                    else
                    {
                        _portAppService.ShowNotification("The entered URL is the same as the current URL. No changes were made.", "No Change");
                    }
                }
                catch (Exception ex)
                {
                    _portAppService.ShowNotification($"Failed to update URL and restart application: {ex.Message}", "Error");
                }
            }
            else
            {
                _portAppService.ShowNotification("Please enter a valid URL.", "Invalid Input");
            }
        }
        private void RestartApplication()
        {
            try
            {

                // Close the current application
                Application.Exit();

                // Start a new instance of the application
                Process.Start(Application.ExecutablePath);
            }
            catch (Exception ex)
            {
                _portAppService.ShowNotification($"Failed to restart the application: {ex.Message}", "Error");
                // Optionally log the exception
            }
        }

    }




}
