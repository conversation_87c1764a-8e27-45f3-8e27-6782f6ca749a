<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <!-- Form Titles -->
  <data name="BarcodeFormTitle" xml:space="preserve">
    <value>إعدادات الباركود</value>
  </data>
  <data name="ScannerFormTitle" xml:space="preserve">
    <value>إعدادات الماسح الضوئي</value>
  </data>
  <!-- Tab Names -->
  <data name="BarcodeTabText" xml:space="preserve">
    <value>إعدادات الباركود</value>
  </data>
  <data name="ScannerTabText" xml:space="preserve">
    <value>إعدادات الماسح</value>
  </data>
  <!-- Labels -->
  <data name="BarcodeUrlLabel" xml:space="preserve">
    <value>رابط الباركود:</value>
  </data>
  <data name="ComPortLabel" xml:space="preserve">
    <value>منفذ COM:</value>
  </data>
  <data name="AvailablePortsLabel" xml:space="preserve">
    <value>المنافذ المتاحة:</value>
  </data>
  <data name="AvailableScannersLabel" xml:space="preserve">
    <value>الماسحات المتاحة:</value>
  </data>
  <!-- Buttons -->
  <data name="SaveBarcodeConfigButton" xml:space="preserve">
    <value>حفظ إعدادات الباركود</value>
  </data>
  <data name="SaveScannerConfigButton" xml:space="preserve">
    <value>حفظ إعدادات الماسح</value>
  </data>
  <data name="TestConnectionButton" xml:space="preserve">
    <value>اختبار الاتصال</value>
  </data>
  <data name="SendManualDataButton" xml:space="preserve">
    <value>إرسال البيانات يدوياً</value>
  </data>
  <!-- Checkboxes -->
  <data name="UseBarcodeReaderCheckbox" xml:space="preserve">
    <value>استخدام قارئ الباركود</value>
  </data>
  <!-- Messages -->
  <data name="ConfigurationSavedMessage" xml:space="preserve">
    <value>تم حفظ الإعدادات بنجاح!</value>
  </data>
  <data name="ConfigurationFailedMessage" xml:space="preserve">
    <value>فشل في حفظ الإعدادات. يرجى المحاولة مرة أخرى.</value>
  </data>
  <data name="ConnectionSuccessMessage" xml:space="preserve">
    <value>نجح اختبار الاتصال!</value>
  </data>
  <data name="ConnectionFailedMessage" xml:space="preserve">
    <value>فشل اختبار الاتصال. يرجى التحقق من الإعدادات.</value>
  </data>
  <data name="InvalidUrlMessage" xml:space="preserve">
    <value>يرجى إدخال رابط صحيح.</value>
  </data>
  <data name="NoPortSelectedMessage" xml:space="preserve">
    <value>يرجى اختيار منفذ COM.</value>
  </data>
  <data name="NoScannerSelectedMessage" xml:space="preserve">
    <value>يرجى اختيار ماسح ضوئي.</value>
  </data>
  <data name="DataSentMessage" xml:space="preserve">
    <value>تم إرسال البيانات اليدوية بنجاح!</value>
  </data>
  <data name="DataSendFailedMessage" xml:space="preserve">
    <value>فشل في إرسال البيانات اليدوية.</value>
  </data>
</root>
