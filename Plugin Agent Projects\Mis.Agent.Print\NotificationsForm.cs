﻿
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Mis.Agent.Print
{
    public partial class NotificationsForm : Form
    {
        private DataTable? _notificationsTable;
        INotificationAppService _notificationService;

        public NotificationsForm(INotificationAppService notificationService)
        {
            _notificationService = notificationService;
            _notificationsTable = new DataTable();
            InitializeComponent();
            InitializeLocalization();
        }

        private void InitializeLocalization()
        {
            // Subscribe to culture changes
            SimpleLocalization.CultureChanged += OnCultureChanged;

            // Apply initial localization
            ApplyLocalization();
        }

        private void OnCultureChanged(object sender, CultureChangedEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnCultureChanged(sender, e)));
                return;
            }

            ApplyLocalization();
        }

        private void ApplyLocalization()
        {
            // Update form title
            this.Text = SimpleLocalization.GetString("NotificationsFormTitle", "Notifications Management");

            // Update tab text - ensure it's never null, keep original if localization fails
            var originalText = NotificationsTab.Text; // Keep the designer value as fallback
            var tabText = SimpleLocalization.GetString("NotificationsTabText", "Notifications");
            NotificationsTab.Text = string.IsNullOrEmpty(tabText) ? (string.IsNullOrEmpty(originalText) ? "Notifications" : originalText) : tabText;

            // Update checkboxes
            checkBox1.Text = SimpleLocalization.GetString("EnableNotificationsCheckbox", "Enable/Disable Notifications");

            // Update buttons
            btnClearNotifications.Text = SimpleLocalization.GetString("ClearNotificationsButton", "Clear All Notifications");

            // Update column headers if DataGridView is initialized
            if (dataGridView1.Columns.Count > 0)
            {
                UpdateDataGridViewHeaders();
            }
        }

        private void UpdateDataGridViewHeaders()
        {
            if (_notificationsTable != null && _notificationsTable.Columns.Count > 0)
            {
                // Update column headers
                if (_notificationsTable.Columns.Contains("Id"))
                    _notificationsTable.Columns["Id"].ColumnName = SimpleLocalization.GetString("NotificationIdColumn", "ID");
                if (_notificationsTable.Columns.Contains("No"))
                    _notificationsTable.Columns["No"].ColumnName = SimpleLocalization.GetString("NotificationNumberColumn", "Number");
                if (_notificationsTable.Columns.Contains("IsPrinted"))
                    _notificationsTable.Columns["IsPrinted"].ColumnName = SimpleLocalization.GetString("IsPrintedColumn", "Is Printed");
                if (_notificationsTable.Columns.Contains("ReceiveTime"))
                    _notificationsTable.Columns["ReceiveTime"].ColumnName = SimpleLocalization.GetString("ReceiveTimeColumn", "Receive Time");
            }
        }
        private void checkBox1_CheckedChanged(object sender, EventArgs e)
        {
            var args = new NotificationEventArgs
            {
                IsEnabled = checkBox1.Checked
            };
            NotificationManager.Notify(args);


        }
        public async void PopulateForm()
        {
            try
            {
                if (_notificationsTable == null) return;

                // Clear the existing data
                _notificationsTable.Clear();

                // Ensure the DataTable has the correct columns
                if (_notificationsTable.Columns.Count == 0)
                {
                    _notificationsTable.Columns.Add("Id", typeof(Guid));
                    _notificationsTable.Columns.Add("No", typeof(string));
                    _notificationsTable.Columns.Add("IsPrinted", typeof(bool));
                    _notificationsTable.Columns.Add("ReceiveTime", typeof(DateTime));
                }

                // Fetch the latest notifications from the database
                var notifications = new List<TransactionDto>(); // TODO: Implement proper data fetching

                // Populate the DataTable with the fetched notifications
                foreach (var notification in notifications)
                {
                    _notificationsTable.Rows.Add(notification.Id, notification.No, notification.IsPrinted, notification.ReceiveTime);
                }

                // Refresh the DataGridView to display the updated data
                dataGridView1.DataSource = _notificationsTable;
                dataGridView1.Refresh();
            }
            catch (Exception ex)
            {
                string errorTitle = SimpleLocalization.GetString("Error", "Error");
                string errorMessage = SimpleLocalization.GetString("ErrorOccurred", "An error occurred") + ": " + ex.Message;
                MessageBox.Show(errorMessage, errorTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void BtnClearNotifications_Click(object sender, EventArgs e)
        {
            try
            {
                // Show confirmation dialog
                string confirmTitle = SimpleLocalization.GetString("Confirmation", "Confirmation");
                string confirmMessage = SimpleLocalization.GetString("ClearNotificationsConfirmMessage", "Are you sure you want to clear all notifications?");

                DialogResult result = MessageBox.Show(confirmMessage, confirmTitle, MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // Clear all notifications
                    await _notificationService.ClearAllNotificationsAsync();

                    // Refresh the form
                    PopulateForm();

                    // Show success notification
                    string successTitle = SimpleLocalization.GetString("Success", "Success");
                    string successMessage = SimpleLocalization.GetString("NotificationsClearedMessage", "All notifications cleared successfully!");
                    MessageBox.Show(successMessage, successTitle, MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                string errorTitle = SimpleLocalization.GetString("Error", "Error");
                string errorMessage = SimpleLocalization.GetString("ClearNotificationsFailedMessage", "Failed to clear notifications.") + " " + ex.Message;
                MessageBox.Show(errorMessage, errorTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }


    }
}
