<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <!-- Form Titles -->
  <data name="BarcodeFormTitle" xml:space="preserve">
    <value>Barcode Configuration</value>
  </data>
  <data name="ScannerFormTitle" xml:space="preserve">
    <value>Scanner Configuration</value>
  </data>
  <!-- Tab Names -->
  <data name="BarcodeTabText" xml:space="preserve">
    <value>Barcode Settings</value>
  </data>
  <data name="ScannerTabText" xml:space="preserve">
    <value>Scanner Settings</value>
  </data>
  <!-- Labels -->
  <data name="BarcodeUrlLabel" xml:space="preserve">
    <value>Barcode URL:</value>
  </data>
  <data name="ComPortLabel" xml:space="preserve">
    <value>COM Port:</value>
  </data>
  <data name="AvailablePortsLabel" xml:space="preserve">
    <value>Available Ports:</value>
  </data>
  <data name="AvailableScannersLabel" xml:space="preserve">
    <value>Available Scanners:</value>
  </data>
  <!-- Buttons -->
  <data name="SaveBarcodeConfigButton" xml:space="preserve">
    <value>Save Barcode Configuration</value>
  </data>
  <data name="SaveScannerConfigButton" xml:space="preserve">
    <value>Save Scanner Configuration</value>
  </data>
  <data name="TestConnectionButton" xml:space="preserve">
    <value>Test Connection</value>
  </data>
  <data name="SendManualDataButton" xml:space="preserve">
    <value>Send Manual Data</value>
  </data>
  <!-- Checkboxes -->
  <data name="UseBarcodeReaderCheckbox" xml:space="preserve">
    <value>Use Barcode Reader</value>
  </data>
  <!-- Messages -->
  <data name="ConfigurationSavedMessage" xml:space="preserve">
    <value>Configuration saved successfully!</value>
  </data>
  <data name="ConfigurationFailedMessage" xml:space="preserve">
    <value>Failed to save configuration. Please try again.</value>
  </data>
  <data name="ConnectionSuccessMessage" xml:space="preserve">
    <value>Connection test successful!</value>
  </data>
  <data name="ConnectionFailedMessage" xml:space="preserve">
    <value>Connection test failed. Please check your settings.</value>
  </data>
  <data name="InvalidUrlMessage" xml:space="preserve">
    <value>Please enter a valid URL.</value>
  </data>
  <data name="NoPortSelectedMessage" xml:space="preserve">
    <value>Please select a COM port.</value>
  </data>
  <data name="NoScannerSelectedMessage" xml:space="preserve">
    <value>Please select a scanner.</value>
  </data>
  <data name="DataSentMessage" xml:space="preserve">
    <value>Manual data sent successfully!</value>
  </data>
  <data name="DataSendFailedMessage" xml:space="preserve">
    <value>Failed to send manual data.</value>
  </data>
</root>
