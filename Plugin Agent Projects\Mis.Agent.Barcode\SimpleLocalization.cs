using System;
using System.Collections.Generic;
using System.Globalization;

namespace Mis.Agent.Barcode
{
    public class CultureChangedEventArgs : EventArgs
    {
        public CultureInfo? OldCulture { get; set; }
        public CultureInfo? NewCulture { get; set; }
        public bool IsRightToLeft { get; set; }
    }

    public static class SimpleLocalization
    {
        private static CultureInfo _currentCulture = CultureInfo.CurrentCulture;
        private static readonly Dictionary<string, string> _englishStrings = new();
        private static readonly Dictionary<string, string> _arabicStrings = new();

        public static event EventHandler<CultureChangedEventArgs>? CultureChanged;

        static SimpleLocalization()
        {
            InitializeStrings();
        }

        public static CultureInfo CurrentCulture => _currentCulture;

        public static void SetCulture(string cultureName)
        {
            var oldCulture = _currentCulture;
            var culture = new CultureInfo(cultureName);
            _currentCulture = culture;
            CultureInfo.CurrentCulture = culture;
            CultureInfo.CurrentUICulture = culture;

            var isRtl = culture.TextInfo.IsRightToLeft;
            CultureChanged?.Invoke(null, new CultureChangedEventArgs
            {
                OldCulture = oldCulture,
                NewCulture = culture,
                IsRightToLeft = isRtl
            });
        }

        public static string GetString(string key, string defaultValue = "")
        {
            var strings = _currentCulture.Name == "ar-SA" ? _arabicStrings : _englishStrings;
            return strings.TryGetValue(key, out var value) ? value : defaultValue;
        }

        private static void InitializeStrings()
        {
            // English strings
            _englishStrings["BarcodeFormTitle"] = "Barcode Scanner";
            _englishStrings["BarcodeUrlLabel"] = "Barcode URL:";
            _englishStrings["ComPortLabel"] = "COM Port:";
            _englishStrings["ConnectButton"] = "Connect";
            _englishStrings["DisconnectButton"] = "Disconnect";
            _englishStrings["ScanButton"] = "Scan";

            // Arabic strings
            _arabicStrings["BarcodeFormTitle"] = "ماسح الباركود";
            _arabicStrings["BarcodeUrlLabel"] = "رابط الباركود:";
            _arabicStrings["ComPortLabel"] = "منفذ COM:";
            _arabicStrings["ConnectButton"] = "اتصال";
            _arabicStrings["DisconnectButton"] = "قطع الاتصال";
            _arabicStrings["ScanButton"] = "مسح";
        }
    }
}
